// src/components/dialog/CheckInDialog.tsx
'use client'

import { Dialog, DialogContent } from '@/components/ui/dialog'
import Image from 'next/image'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import RoundIconButton from '@/components/common/RoundIconButton'
import HomeIcon from '@/assets/dialog/home.svg'
import Stamp from '@/assets/dialog/stamp.svg'
import { useGameStore } from '@/stores/useGameStore'
import { useGameCampaign } from '@/hooks/useGameCampaign'
import { useNativeStore } from '@/stores/useNativeStore'

interface CheckInDialogProps {
  open: boolean
  onClose: () => void
}

export default function CheckInDialog({ open, onClose }: CheckInDialogProps) {
  const { gameCampaignId } = useGameStore()
  const { userId, versionAppName } = useNativeStore()
  const { mutateAsync: refreshGameCampaign } = useGameCampaign()
  const setGameCampaign = useGameStore((state) => state.setGameCampaign)

  const handleClose = async () => {
    try {
      if (gameCampaignId && userId && versionAppName) {
        const data = await refreshGameCampaign({
          userId,
          appVersion: versionAppName,
          from: 'WEBVIEW'
        })
        setGameCampaign(data)
      }
    } catch (err) {
      console.error('Check-in markSeenPopup failed:', err)
    } finally {
      onClose()
    }
  }
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent showCloseButton={false} className='border-none bg-transparent p-0 shadow-none'>
        <AspectRatio ratio={1560 / 2720} className='relative'>
          <Image src='/dialog/checkInBG.png' alt='Check In' fill quality={100} />
          {/* Centered content */}
          <div className='font-montserrat relative top-[42%] flex h-full w-full justify-center text-[12px]'>
            <div className='flex h-[25%] w-1/2 flex-col items-center justify-center gap-[12%]'>
              <div className='w-[35%]'>
                <AspectRatio ratio={955 / 968} className='relative'>
                  <Image
                    src='/dialog/mooncake.png'
                    alt='Check In'
                    fill
                    quality={100}
                    className='drop-shadow-[5px_2px_20px_rgba(0,0,0,0.3)]'
                  />
                  {/* Small red badge with stamp and text */}
                  <div className='absolute right-0 bottom-0 w-[30%]'>
                    <AspectRatio ratio={1} className='relative'>
                      <div className='absolute inset-0 flex items-center justify-center'>
                        <Stamp className='h-full w-full' />
                      </div>
                      <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 mt-[10%] ml-[5%] flex items-center justify-center text-[10px] font-bold text-white'>
                        x1
                      </span>
                    </AspectRatio>
                  </div>
                </AspectRatio>
              </div>

              <div className='flex flex-col items-center justify-center gap-[1%]'>
                <p className='font-medium text-white'>Bạn đã nhận được</p>
                <p className='font-bold text-[#FFFF70]'>1 Bánh Trung Thu</p>
              </div>
            </div>
          </div>
          {/* Absolute blue div with buttons */}
          <div className='absolute top-[69.5%] left-1/2 flex h-[7.5%] w-[26%] -translate-x-1/2 items-center justify-center'>
            <RoundIconButton
              className='w-[10.6dvw]'
              icon={<HomeIcon className='z-20 h-[50%] w-[50%]' />}
              onClick={handleClose}
            />
          </div>
        </AspectRatio>
      </DialogContent>
    </Dialog>
  )
}
