// src/app/api/getRankingGameCampaign/route.ts
import { NextResponse } from 'next/server'
import axios, { AxiosError } from 'axios'

interface RankingRequest {
  [key: string]: unknown
}

interface RankingResponse {
  [key: string]: unknown
}

export async function POST(req: Request) {
  const body: RankingRequest = await req.json()
  console.log('Ranking API - Incoming Request:', body)

  // Get token from request body or use fallback
  const authToken =
    body.token ||
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJraWQiOiJqd3VRcWwzOFJIYzFyTHltY2M4RmpzZ2dBV0w3ck0yciIsInVpZCI6IngxNjkwNzlmZDczYTQ1MzNkNGZkNWU3OTA5NzEyMmE5YiIsImV4cCI6MTc1OTE0MTM4OH0.U19XYfTHpIO6vU_m8gYKGV4DAT8WVd9c2H5gG4D5Y6U'

  try {
    const { data } = await axios.post<RankingResponse>(
      `${process.env.BE_URL}/api-asker-vn/get-ranking-game-campaign`,
      body,
      {
        headers: {
          accessKey: process.env.ACCESS_KEY_BE as string,
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      }
    )

    console.log('Ranking API - External API Response:', data)

    return NextResponse.json(data)
  } catch (err) {
    const error = err as AxiosError
    console.error('Ranking API - Error:', {
      message: error.message,
      status: error.response?.status,
      response: error.response?.data
    })

    return NextResponse.json({ error: error.message }, { status: error.response?.status ?? 500 })
  }
}
